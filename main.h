#ifndef __MAIN_H__
#define __MAIN_H__

#include <reg51.h>
#include <intrins.h>

// 系统时钟频率定义
#define FOSC 11059200L

// 硬件引脚定义
// 循迹传感器引脚
sbit TRACK_LEFT = P1^0;   // 左循迹传感器
sbit TRACK_RIGHT = P1^1;  // 右循迹传感器

// 电机控制引脚
sbit MOTOR_LEFT_1 = P2^0;   // 左电机正转
sbit MOTOR_LEFT_2 = P2^1;   // 左电机反转
sbit MOTOR_RIGHT_1 = P2^2;  // 右电机正转
sbit MOTOR_RIGHT_2 = P2^3;  // 右电机反转

// LED指示灯引脚
sbit LED1 = P3^0;  // LED1 - 左转指示
sbit LED2 = P3^1;  // LED2 - 右转指示

// OLED显示屏引脚 (I2C接口)
sbit OLED_SCL = P3^6;  // OLED时钟线
sbit OLED_SDA = P3^7;  // OLED数据线

// 小车运动状态定义
#define CAR_STOP      0
#define CAR_FORWARD   1
#define CAR_BACKWARD  2
#define CAR_LEFT      3
#define CAR_RIGHT     4

// 循迹传感器状态定义
#define TRACK_BLACK   0  // 检测到黑线
#define TRACK_WHITE   1  // 检测到白色

// 函数声明
// 系统初始化
void System_Init(void);
void Delay_ms(unsigned int ms);

// 电机控制函数
void Motor_Stop(void);
void Motor_Forward(void);
void Motor_Backward(void);
void Motor_Left(void);
void Motor_Right(void);

// LED控制函数
void LED_Control(unsigned char state);

// 循迹传感器函数
void Track_Read(void);

// OLED显示函数
void OLED_Init(void);
void OLED_Clear(void);
void OLED_ShowString(unsigned char x, unsigned char y, unsigned char *str);
void OLED_ShowChar(unsigned char x, unsigned char y, unsigned char chr);
void OLED_Display_Status(unsigned char car_state);

// I2C通信函数
void I2C_Start(void);
void I2C_Stop(void);
void I2C_SendByte(unsigned char dat);
unsigned char I2C_WaitAck(void);

// 全局变量声明
extern unsigned char car_status;
extern unsigned char track_left_status;
extern unsigned char track_right_status;

#endif
