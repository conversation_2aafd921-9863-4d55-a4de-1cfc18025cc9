#include "main.h"

// OLED显示缓存和字符库
unsigned char code OLED_Font[][6] = {
    {0x00, 0x00, 0x00, 0x00, 0x00, 0x00}, // 空格
    {0x00, 0x00, 0x5F, 0x00, 0x00, 0x00}, // !
    {0x00, 0x07, 0x00, 0x07, 0x00, 0x00}, // "
    {0x14, 0x7F, 0x14, 0x7F, 0x14, 0x00}, // #
    {0x24, 0x2A, 0x7F, 0x2A, 0x12, 0x00}, // $
    {0x23, 0x13, 0x08, 0x64, 0x62, 0x00}, // %
    {0x36, 0x49, 0x55, 0x22, 0x50, 0x00}, // &
    {0x00, 0x05, 0x03, 0x00, 0x00, 0x00}, // '
    {0x00, 0x1C, 0x22, 0x41, 0x00, 0x00}, // (
    {0x00, 0x41, 0x22, 0x1C, 0x00, 0x00}, // )
    {0x08, 0x2A, 0x1C, 0x2A, 0x08, 0x00}, // *
    {0x08, 0x08, 0x3E, 0x08, 0x08, 0x00}, // +
    {0x00, 0x50, 0x30, 0x00, 0x00, 0x00}, // ,
    {0x08, 0x08, 0x08, 0x08, 0x08, 0x00}, // -
    {0x00, 0x60, 0x60, 0x00, 0x00, 0x00}, // .
    {0x20, 0x10, 0x08, 0x04, 0x02, 0x00}, // /
    {0x3E, 0x51, 0x49, 0x45, 0x3E, 0x00}, // 0
    {0x00, 0x42, 0x7F, 0x40, 0x00, 0x00}, // 1
    {0x42, 0x61, 0x51, 0x49, 0x46, 0x00}, // 2
    {0x21, 0x41, 0x45, 0x4B, 0x31, 0x00}, // 3
    {0x18, 0x14, 0x12, 0x7F, 0x10, 0x00}, // 4
    {0x27, 0x45, 0x45, 0x45, 0x39, 0x00}, // 5
    {0x3C, 0x4A, 0x49, 0x49, 0x30, 0x00}, // 6
    {0x01, 0x71, 0x09, 0x05, 0x03, 0x00}, // 7
    {0x36, 0x49, 0x49, 0x49, 0x36, 0x00}, // 8
    {0x06, 0x49, 0x49, 0x29, 0x1E, 0x00}, // 9
    {0x00, 0x36, 0x36, 0x00, 0x00, 0x00}, // :
    {0x00, 0x56, 0x36, 0x00, 0x00, 0x00}, // ;
    {0x00, 0x08, 0x14, 0x22, 0x41, 0x00}, // <
    {0x14, 0x14, 0x14, 0x14, 0x14, 0x00}, // =
    {0x41, 0x22, 0x14, 0x08, 0x00, 0x00}, // >
    {0x02, 0x01, 0x51, 0x09, 0x06, 0x00}, // ?
    {0x32, 0x49, 0x79, 0x41, 0x3E, 0x00}, // @
    {0x7E, 0x11, 0x11, 0x11, 0x7E, 0x00}, // A
    {0x7F, 0x49, 0x49, 0x49, 0x36, 0x00}, // B
    {0x3E, 0x41, 0x41, 0x41, 0x22, 0x00}, // C
    {0x7F, 0x41, 0x41, 0x22, 0x1C, 0x00}, // D
    {0x7F, 0x49, 0x49, 0x49, 0x41, 0x00}, // E
    {0x7F, 0x09, 0x09, 0x01, 0x01, 0x00}, // F
    {0x3E, 0x41, 0x41, 0x51, 0x32, 0x00}, // G
    {0x7F, 0x08, 0x08, 0x08, 0x7F, 0x00}, // H
    {0x00, 0x41, 0x7F, 0x41, 0x00, 0x00}, // I
    {0x20, 0x40, 0x41, 0x3F, 0x01, 0x00}, // J
    {0x7F, 0x08, 0x14, 0x22, 0x41, 0x00}, // K
    {0x7F, 0x40, 0x40, 0x40, 0x40, 0x00}, // L
    {0x7F, 0x02, 0x04, 0x02, 0x7F, 0x00}, // M
    {0x7F, 0x04, 0x08, 0x10, 0x7F, 0x00}, // N
    {0x3E, 0x41, 0x41, 0x41, 0x3E, 0x00}, // O
    {0x7F, 0x09, 0x09, 0x09, 0x06, 0x00}, // P
    {0x3E, 0x41, 0x51, 0x21, 0x5E, 0x00}, // Q
    {0x7F, 0x09, 0x19, 0x29, 0x46, 0x00}, // R
    {0x46, 0x49, 0x49, 0x49, 0x31, 0x00}, // S
    {0x01, 0x01, 0x7F, 0x01, 0x01, 0x00}, // T
    {0x3F, 0x40, 0x40, 0x40, 0x3F, 0x00}, // U
    {0x1F, 0x20, 0x40, 0x20, 0x1F, 0x00}, // V
    {0x7F, 0x20, 0x18, 0x20, 0x7F, 0x00}, // W
    {0x63, 0x14, 0x08, 0x14, 0x63, 0x00}, // X
    {0x03, 0x04, 0x78, 0x04, 0x03, 0x00}, // Y
    {0x61, 0x51, 0x49, 0x45, 0x43, 0x00}  // Z
};

/**
 * @brief I2C起始信号
 */
void I2C_Start(void)
{
    OLED_SDA = 1;
    OLED_SCL = 1;
    _nop_();
    _nop_();
    OLED_SDA = 0;
    _nop_();
    _nop_();
    OLED_SCL = 0;
}

/**
 * @brief I2C停止信号
 */
void I2C_Stop(void)
{
    OLED_SDA = 0;
    OLED_SCL = 1;
    _nop_();
    _nop_();
    OLED_SDA = 1;
}

/**
 * @brief I2C发送一个字节
 * @param dat 要发送的数据
 */
void I2C_SendByte(unsigned char dat)
{
    unsigned char i;
    
    for(i = 0; i < 8; i++)
    {
        OLED_SCL = 0;
        if(dat & 0x80)
            OLED_SDA = 1;
        else
            OLED_SDA = 0;
        _nop_();
        OLED_SCL = 1;
        _nop_();
        _nop_();
        dat <<= 1;
    }
    OLED_SCL = 0;
}

/**
 * @brief I2C等待应答
 * @return 应答状态
 */
unsigned char I2C_WaitAck(void)
{
    unsigned char ack;
    
    OLED_SDA = 1;
    _nop_();
    OLED_SCL = 1;
    _nop_();
    _nop_();
    ack = OLED_SDA;
    OLED_SCL = 0;
    
    return ack;
}

/**
 * @brief OLED写命令
 * @param cmd 命令字节
 */
void OLED_WriteCmd(unsigned char cmd)
{
    I2C_Start();
    I2C_SendByte(0x78);  // OLED地址
    I2C_WaitAck();
    I2C_SendByte(0x00);  // 命令模式
    I2C_WaitAck();
    I2C_SendByte(cmd);
    I2C_WaitAck();
    I2C_Stop();
}

/**
 * @brief OLED写数据
 * @param dat 数据字节
 */
void OLED_WriteData(unsigned char dat)
{
    I2C_Start();
    I2C_SendByte(0x78);  // OLED地址
    I2C_WaitAck();
    I2C_SendByte(0x40);  // 数据模式
    I2C_WaitAck();
    I2C_SendByte(dat);
    I2C_WaitAck();
    I2C_Stop();
}

/**
 * @brief 设置OLED显示位置
 * @param x 列位置 (0-127)
 * @param y 页位置 (0-7)
 */
void OLED_SetPos(unsigned char x, unsigned char y)
{
    OLED_WriteCmd(0xB0 + y);                    // 设置页地址
    OLED_WriteCmd(((x & 0xF0) >> 4) | 0x10);   // 设置列地址高4位
    OLED_WriteCmd((x & 0x0F) | 0x01);          // 设置列地址低4位
}

/**
 * @brief OLED初始化
 */
void OLED_Init(void)
{
    Delay_ms(100);  // 等待OLED稳定

    OLED_WriteCmd(0xAE); // 关闭显示
    OLED_WriteCmd(0x20); // 设置内存地址模式
    OLED_WriteCmd(0x10); // 00,水平地址模式;01,垂直地址模式;10,页地址模式(RESET);11,无效
    OLED_WriteCmd(0xB0); // 设置页起始地址为页0
    OLED_WriteCmd(0xC8); // 设置COM扫描方向
    OLED_WriteCmd(0x00); // 设置低列地址
    OLED_WriteCmd(0x10); // 设置高列地址
    OLED_WriteCmd(0x40); // 设置起始行地址
    OLED_WriteCmd(0x81); // 设置对比度控制寄存器
    OLED_WriteCmd(0xFF); // 设置对比度
    OLED_WriteCmd(0xA1); // 设置段重定义设置,bit0:0,0->0;1,0->127;
    OLED_WriteCmd(0xA6); // 设置显示方式;bit0:1,反相显示;0,正常显示
    OLED_WriteCmd(0xA8); // 设置驱动路数
    OLED_WriteCmd(0x3F); // 默认0X3F(1/64)
    OLED_WriteCmd(0xA4); // 全局显示开启;bit0:1,开启;0,关闭;(白屏/黑屏)
    OLED_WriteCmd(0xD3); // 设置显示偏移
    OLED_WriteCmd(0x00); // 默认为0
    OLED_WriteCmd(0xD5); // 设置时钟分频因子,震荡频率
    OLED_WriteCmd(0x80); // 设置分频因子,高4bit是分频因子,低4bit是震荡频率
    OLED_WriteCmd(0xD9); // 设置预充电周期
    OLED_WriteCmd(0xF1); // [3:0],PHASE 1;[7:4],PHASE 2;
    OLED_WriteCmd(0xDA); // 设置COM硬件引脚配置
    OLED_WriteCmd(0x12);
    OLED_WriteCmd(0xDB); // 设置VCOMH 电压倍率
    OLED_WriteCmd(0x40); // [6:4] 000,0.65*vcc;001,0.77*vcc;011,0.83*vcc;
    OLED_WriteCmd(0x8D); // 设置DC-DC开启
    OLED_WriteCmd(0x14); //
    OLED_WriteCmd(0xAF); // 开启显示
}

/**
 * @brief OLED清屏
 */
void OLED_Clear(void)
{
    unsigned char i, j;

    for(i = 0; i < 8; i++)
    {
        OLED_SetPos(0, i);
        for(j = 0; j < 128; j++)
        {
            OLED_WriteData(0x00);
        }
    }
}

/**
 * @brief OLED显示字符
 * @param x 列位置 (0-127)
 * @param y 页位置 (0-7)
 * @param chr 要显示的字符
 */
void OLED_ShowChar(unsigned char x, unsigned char y, unsigned char chr)
{
    unsigned char i;

    chr = chr - ' ';  // 得到偏移后的值
    OLED_SetPos(x, y);

    for(i = 0; i < 6; i++)
    {
        OLED_WriteData(OLED_Font[chr][i]);
    }
}

/**
 * @brief OLED显示字符串
 * @param x 起始列位置 (0-127)
 * @param y 页位置 (0-7)
 * @param str 要显示的字符串
 */
void OLED_ShowString(unsigned char x, unsigned char y, unsigned char *str)
{
    while(*str != '\0')
    {
        OLED_ShowChar(x, y, *str);
        x += 6;
        if(x > 122)
        {
            x = 0;
            y++;
        }
        str++;
    }
}

/**
 * @brief OLED显示小车状态
 * @param car_state 小车运动状态
 */
void OLED_Display_Status(unsigned char car_state)
{
    // 清除状态显示区域
    OLED_ShowString(0, 4, "Status:         ");
    OLED_ShowString(0, 6, "Sensor: L  R    ");

    // 显示小车运动状态
    switch(car_state)
    {
        case CAR_STOP:
            OLED_ShowString(48, 4, "STOP    ");
            break;

        case CAR_FORWARD:
            OLED_ShowString(48, 4, "FORWARD ");
            break;

        case CAR_BACKWARD:
            OLED_ShowString(48, 4, "BACKWARD");
            break;

        case CAR_LEFT:
            OLED_ShowString(48, 4, "LEFT    ");
            break;

        case CAR_RIGHT:
            OLED_ShowString(48, 4, "RIGHT   ");
            break;

        default:
            OLED_ShowString(48, 4, "UNKNOWN ");
            break;
    }

    // 显示传感器状态
    if(track_left_status == TRACK_BLACK)
        OLED_ShowChar(60, 6, '1');
    else
        OLED_ShowChar(60, 6, '0');

    if(track_right_status == TRACK_BLACK)
        OLED_ShowChar(78, 6, '1');
    else
        OLED_ShowChar(78, 6, '0');
}

/**
 * @brief OLED显示启动画面
 */
void OLED_Show_Startup(void)
{
    OLED_Clear();
    OLED_ShowString(20, 0, "STC51 Car");
    OLED_ShowString(10, 2, "Track Following");
    OLED_ShowString(30, 4, "System");
    OLED_ShowString(15, 6, "Initializing...");
}

/**
 * @brief OLED显示系统信息
 */
void OLED_Show_System_Info(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "STC89C51 Car V1.0");
    OLED_ShowString(0, 2, "Track Sensor: OK");
    OLED_ShowString(0, 4, "Motor Driver: OK");
    OLED_ShowString(0, 6, "LED Status: OK");
}
