#include "main.h"

/**
 * @brief 电机停止
 */
void Motor_Stop(void)
{
    MOTOR_LEFT_1 = 0;
    MOTOR_LEFT_2 = 0;
    MOTOR_RIGHT_1 = 0;
    MOTOR_RIGHT_2 = 0;
}

/**
 * @brief 电机前进
 * 左右电机同时正转
 */
void Motor_Forward(void)
{
    // 左电机正转
    MOTOR_LEFT_1 = 1;
    MOTOR_LEFT_2 = 0;
    
    // 右电机正转
    MOTOR_RIGHT_1 = 1;
    MOTOR_RIGHT_2 = 0;
}

/**
 * @brief 电机后退
 * 左右电机同时反转
 */
void Motor_Backward(void)
{
    // 左电机反转
    MOTOR_LEFT_1 = 0;
    MOTOR_LEFT_2 = 1;
    
    // 右电机反转
    MOTOR_RIGHT_1 = 0;
    MOTOR_RIGHT_2 = 1;
}

/**
 * @brief 电机左转
 * 左电机停止或反转，右电机正转
 */
void Motor_Left(void)
{
    // 左电机停止
    MOTOR_LEFT_1 = 0;
    MOTOR_LEFT_2 = 0;
    
    // 右电机正转
    MOTOR_RIGHT_1 = 1;
    MOTOR_RIGHT_2 = 0;
}

/**
 * @brief 电机右转
 * 左电机正转，右电机停止或反转
 */
void Motor_Right(void)
{
    // 左电机正转
    MOTOR_LEFT_1 = 1;
    MOTOR_LEFT_2 = 0;
    
    // 右电机停止
    MOTOR_RIGHT_1 = 0;
    MOTOR_RIGHT_2 = 0;
}

/**
 * @brief 电机急左转
 * 左电机反转，右电机正转（原地转向）
 */
void Motor_Sharp_Left(void)
{
    // 左电机反转
    MOTOR_LEFT_1 = 0;
    MOTOR_LEFT_2 = 1;
    
    // 右电机正转
    MOTOR_RIGHT_1 = 1;
    MOTOR_RIGHT_2 = 0;
}

/**
 * @brief 电机急右转
 * 左电机正转，右电机反转（原地转向）
 */
void Motor_Sharp_Right(void)
{
    // 左电机正转
    MOTOR_LEFT_1 = 1;
    MOTOR_LEFT_2 = 0;
    
    // 右电机反转
    MOTOR_RIGHT_1 = 0;
    MOTOR_RIGHT_2 = 1;
}
