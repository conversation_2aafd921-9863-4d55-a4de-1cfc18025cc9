# Makefile for STC89C51 Track Following Car
# 使用SDCC编译器

# 编译器设置
CC = sdcc
CFLAGS = -mmcs51 --model-small --stack-auto --iram-size 256 --xram-size 0 --code-size 8192
LDFLAGS = --out-fmt-ihx

# 项目名称
PROJECT = track_car

# 源文件
SOURCES = main.c motor.c track_sensor.c led_control.c oled.c

# 目标文件
OBJECTS = $(SOURCES:.c=.rel)

# 头文件
HEADERS = main.h

# 默认目标
all: $(PROJECT).hex

# 生成HEX文件
$(PROJECT).hex: $(PROJECT).ihx
	packihx $(PROJECT).ihx > $(PROJECT).hex

# 生成IHX文件
$(PROJECT).ihx: $(OBJECTS)
	$(CC) $(CFLAGS) $(LDFLAGS) $(OBJECTS) -o $(PROJECT).ihx

# 编译C文件为REL文件
%.rel: %.c $(HEADERS)
	$(CC) $(CFLAGS) -c $< -o $@

# 清理编译文件
clean:
	rm -f *.rel *.ihx *.hex *.lk *.lst *.map *.mem *.rst *.sym *.asm

# 显示帮助信息
help:
	@echo "STC89C51 循迹小车项目编译说明"
	@echo "================================"
	@echo "make all     - 编译整个项目"
	@echo "make clean   - 清理编译文件"
	@echo "make help    - 显示此帮助信息"
	@echo ""
	@echo "编译要求:"
	@echo "- 安装SDCC编译器"
	@echo "- 确保所有源文件在当前目录"
	@echo ""
	@echo "输出文件:"
	@echo "- $(PROJECT).hex - 用于烧录到单片机的HEX文件"

# 伪目标
.PHONY: all clean help

# 依赖关系
main.rel: main.c main.h
motor.rel: motor.c main.h
track_sensor.rel: track_sensor.c main.h
led_control.rel: led_control.c main.h
oled.rel: oled.c main.h
