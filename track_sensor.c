#include "main.h"

/**
 * @brief 读取循迹传感器状态
 * 更新全局变量track_left_status和track_right_status
 */
void Track_Read(void)
{
    // 读取左循迹传感器
    // 注意：循迹传感器通常是低电平有效（检测到黑线输出低电平）
    track_left_status = TRACK_LEFT;
    
    // 读取右循迹传感器
    track_right_status = TRACK_RIGHT;
}

/**
 * @brief 获取循迹传感器组合状态
 * @return 返回传感器组合状态值
 * 0: 左白右白 (00)
 * 1: 左白右黑 (01) 
 * 2: 左黑右白 (10)
 * 3: 左黑右黑 (11)
 */
unsigned char Track_Get_Status(void)
{
    unsigned char status = 0;
    
    Track_Read();
    
    if(track_left_status == TRACK_BLACK)
        status |= 0x02;  // 设置bit1
    
    if(track_right_status == TRACK_BLACK)
        status |= 0x01;  // 设置bit0
    
    return status;
}

/**
 * @brief 循迹传感器状态判断
 * @return 返回小车应该执行的动作
 */
unsigned char Track_Decision(void)
{
    unsigned char sensor_status;
    
    sensor_status = Track_Get_Status();
    
    switch(sensor_status)
    {
        case 0:  // 00 - 左白右白，脱离轨道
            return CAR_STOP;
            
        case 1:  // 01 - 左白右黑，需要左转
            return CAR_LEFT;
            
        case 2:  // 10 - 左黑右白，需要右转
            return CAR_RIGHT;
            
        case 3:  // 11 - 左黑右黑，直行
            return CAR_FORWARD;
            
        default:
            return CAR_STOP;
    }
}

/**
 * @brief 循迹传感器校准函数
 * 用于在白色背景上校准传感器
 */
void Track_Calibrate_White(void)
{
    // 在白色背景上校准，确保传感器输出高电平
    // 这个函数可以在系统初始化时调用
    Delay_ms(100);  // 等待传感器稳定
}

/**
 * @brief 循迹传感器校准函数
 * 用于在黑线上校准传感器
 */
void Track_Calibrate_Black(void)
{
    // 在黑线上校准，确保传感器输出低电平
    // 这个函数可以在系统初始化时调用
    Delay_ms(100);  // 等待传感器稳定
}
