#include "main.h"

/**
 * @brief LED控制函数
 * @param state 小车运动状态
 * 根据小车运动状态控制LED指示灯
 * 左转时亮LED1，右转时亮LED2，直行时两个都亮，停止时都灭
 */
void LED_Control(unsigned char state)
{
    switch(state)
    {
        case CAR_STOP:
            // 停止状态 - 两个LED都熄灭
            LED1 = 1;  // 共阳极LED，高电平熄灭
            LED2 = 1;
            break;
            
        case CAR_FORWARD:
            // 直行状态 - 两个LED都点亮
            LED1 = 0;  // 共阳极LED，低电平点亮
            LED2 = 0;
            break;
            
        case CAR_BACKWARD:
            // 后退状态 - 两个LED闪烁
            LED1 = 0;
            LED2 = 0;
            break;
            
        case CAR_LEFT:
            // 左转状态 - 只亮LED1
            LED1 = 0;  // 点亮LED1
            LED2 = 1;  // 熄灭LED2
            break;
            
        case CAR_RIGHT:
            // 右转状态 - 只亮LED2
            LED1 = 1;  // 熄灭LED1
            LED2 = 0;  // 点亮LED2
            break;
            
        default:
            // 默认状态 - 两个LED都熄灭
            LED1 = 1;
            LED2 = 1;
            break;
    }
}

/**
 * @brief LED闪烁函数
 * @param led_num LED编号 (1或2)
 * @param times 闪烁次数
 * @param delay_ms 闪烁间隔(毫秒)
 */
void LED_Blink(unsigned char led_num, unsigned char times, unsigned int delay_ms)
{
    unsigned char i;
    
    for(i = 0; i < times; i++)
    {
        if(led_num == 1)
        {
            LED1 = 0;  // 点亮
            Delay_ms(delay_ms);
            LED1 = 1;  // 熄灭
            Delay_ms(delay_ms);
        }
        else if(led_num == 2)
        {
            LED2 = 0;  // 点亮
            Delay_ms(delay_ms);
            LED2 = 1;  // 熄灭
            Delay_ms(delay_ms);
        }
    }
}

/**
 * @brief 两个LED同时闪烁
 * @param times 闪烁次数
 * @param delay_ms 闪烁间隔(毫秒)
 */
void LED_Blink_Both(unsigned char times, unsigned int delay_ms)
{
    unsigned char i;
    
    for(i = 0; i < times; i++)
    {
        LED1 = 0;  // 同时点亮
        LED2 = 0;
        Delay_ms(delay_ms);
        
        LED1 = 1;  // 同时熄灭
        LED2 = 1;
        Delay_ms(delay_ms);
    }
}

/**
 * @brief LED流水灯效果
 * @param times 循环次数
 * @param delay_ms 切换间隔(毫秒)
 */
void LED_Running_Light(unsigned char times, unsigned int delay_ms)
{
    unsigned char i;
    
    for(i = 0; i < times; i++)
    {
        // LED1亮，LED2灭
        LED1 = 0;
        LED2 = 1;
        Delay_ms(delay_ms);
        
        // LED1灭，LED2亮
        LED1 = 1;
        LED2 = 0;
        Delay_ms(delay_ms);
    }
    
    // 最后都熄灭
    LED1 = 1;
    LED2 = 1;
}

/**
 * @brief LED测试函数
 * 系统启动时测试LED是否正常工作
 */
void LED_Test(void)
{
    // 测试LED1
    LED1 = 0;
    LED2 = 1;
    Delay_ms(500);
    
    // 测试LED2
    LED1 = 1;
    LED2 = 0;
    Delay_ms(500);
    
    // 测试两个LED同时亮
    LED1 = 0;
    LED2 = 0;
    Delay_ms(500);
    
    // 全部熄灭
    LED1 = 1;
    LED2 = 1;
}
