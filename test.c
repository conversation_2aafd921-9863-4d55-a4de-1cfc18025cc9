#include "main.h"

/**
 * @brief 硬件测试程序
 * 用于测试各个模块是否正常工作
 */

// 测试模式选择
#define TEST_LED        1
#define TEST_MOTOR      2
#define TEST_SENSOR     3
#define TEST_OLED       4
#define TEST_ALL        5

unsigned char test_mode = TEST_ALL;

/**
 * @brief LED测试函数
 */
void Test_LED(void)
{
    unsigned char i;
    
    for(i = 0; i < 5; i++)
    {
        // 测试LED1
        LED1 = 0;
        LED2 = 1;
        Delay_ms(500);
        
        // 测试LED2
        LED1 = 1;
        LED2 = 0;
        Delay_ms(500);
        
        // 同时亮
        LED1 = 0;
        LED2 = 0;
        Delay_ms(500);
        
        // 同时灭
        LED1 = 1;
        LED2 = 1;
        Delay_ms(500);
    }
}

/**
 * @brief 电机测试函数
 */
void Test_Motor(void)
{
    // 前进测试
    Motor_Forward();
    Delay_ms(2000);
    Motor_Stop();
    Delay_ms(1000);
    
    // 后退测试
    Motor_Backward();
    Delay_ms(2000);
    Motor_Stop();
    Delay_ms(1000);
    
    // 左转测试
    Motor_Left();
    Delay_ms(2000);
    Motor_Stop();
    Delay_ms(1000);
    
    // 右转测试
    Motor_Right();
    Delay_ms(2000);
    Motor_Stop();
    Delay_ms(1000);
}

/**
 * @brief 传感器测试函数
 */
void Test_Sensor(void)
{
    unsigned char i;
    
    for(i = 0; i < 50; i++)  // 测试5秒
    {
        Track_Read();
        
        // 根据传感器状态控制LED
        if(track_left_status == TRACK_BLACK)
            LED1 = 0;
        else
            LED1 = 1;
            
        if(track_right_status == TRACK_BLACK)
            LED2 = 0;
        else
            LED2 = 1;
            
        Delay_ms(100);
    }
    
    // 关闭LED
    LED1 = 1;
    LED2 = 1;
}

/**
 * @brief OLED测试函数
 */
void Test_OLED(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "OLED Test");
    OLED_ShowString(0, 2, "Line 2");
    OLED_ShowString(0, 4, "Line 4");
    OLED_ShowString(0, 6, "Line 6");
    Delay_ms(3000);
    
    OLED_Clear();
    OLED_ShowString(0, 0, "Numbers:");
    OLED_ShowString(0, 2, "0123456789");
    OLED_ShowString(0, 4, "ABCDEFGHIJ");
    OLED_ShowString(0, 6, "abcdefghij");
    Delay_ms(3000);
}

/**
 * @brief 综合测试函数
 */
void Test_All(void)
{
    OLED_Clear();
    OLED_ShowString(0, 0, "Testing LED...");
    Test_LED();
    
    OLED_Clear();
    OLED_ShowString(0, 0, "Testing Motor...");
    Test_Motor();
    
    OLED_Clear();
    OLED_ShowString(0, 0, "Testing Sensor...");
    OLED_ShowString(0, 2, "Move on track");
    Test_Sensor();
    
    OLED_Clear();
    OLED_ShowString(0, 0, "Testing OLED...");
    Test_OLED();
    
    OLED_Clear();
    OLED_ShowString(0, 2, "All Tests");
    OLED_ShowString(0, 4, "Completed!");
}

/**
 * @brief 测试主函数
 * 注释掉main.c中的main函数，使用这个测试函数
 */
/*
void main(void)
{
    // 系统初始化
    System_Init();
    OLED_Init();
    
    OLED_Clear();
    OLED_ShowString(0, 0, "Hardware Test");
    OLED_ShowString(0, 2, "Starting...");
    Delay_ms(2000);
    
    switch(test_mode)
    {
        case TEST_LED:
            OLED_Clear();
            OLED_ShowString(0, 0, "LED Test");
            Test_LED();
            break;
            
        case TEST_MOTOR:
            OLED_Clear();
            OLED_ShowString(0, 0, "Motor Test");
            Test_Motor();
            break;
            
        case TEST_SENSOR:
            OLED_Clear();
            OLED_ShowString(0, 0, "Sensor Test");
            Test_Sensor();
            break;
            
        case TEST_OLED:
            Test_OLED();
            break;
            
        case TEST_ALL:
        default:
            Test_All();
            break;
    }
    
    while(1)
    {
        // 测试完成后的循环
        LED_Blink_Both(1, 1000);
    }
}
*/
