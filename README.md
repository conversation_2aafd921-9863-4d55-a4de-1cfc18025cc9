# STC89C51 循迹小车项目

## 项目简介

这是一个基于STC89C51单片机的循迹小车项目，具有以下功能：

- 使用二路循迹传感器进行路径检测
- 双电机驱动系统，支持前进、后退、左转、右转
- LED指示灯：左转时亮LED1，右转时亮LED2，直行时两个都亮
- OLED显示屏显示小车状态信息（前进、后退、左转、右转等）

## 硬件连接

### 循迹传感器
- 左循迹传感器：P1.0
- 右循迹传感器：P1.1

### 电机驱动
- 左电机正转：P2.0
- 左电机反转：P2.1
- 右电机正转：P2.2
- 右电机反转：P2.3

### LED指示灯
- LED1（左转指示）：P3.0
- LED2（右转指示）：P3.1

### OLED显示屏（I2C接口）
- SCL时钟线：P3.6
- SDA数据线：P3.7

## 文件结构

```
├── main.h              # 主头文件，包含所有定义和声明
├── main.c              # 主程序文件，包含主循环逻辑
├── motor.c             # 电机控制模块
├── track_sensor.c      # 循迹传感器读取模块
├── led_control.c       # LED控制模块
├── oled.c              # OLED显示驱动模块
├── Makefile            # 编译配置文件
└── README.md           # 项目说明文档
```

## 编译说明

### 使用SDCC编译器

1. 安装SDCC编译器
2. 在项目目录下运行：
   ```bash
   make all
   ```
3. 生成的`track_car.hex`文件可以直接烧录到STC89C51单片机

### 使用Keil C51

1. 创建新项目，选择STC89C51芯片
2. 添加所有.c文件到项目
3. 设置晶振频率为11.0592MHz
4. 编译生成HEX文件

## 工作原理

### 循迹逻辑

| 左传感器 | 右传感器 | 动作 | LED状态 |
|---------|---------|------|---------|
| 黑线(0) | 黑线(0) | 直行 | LED1+LED2亮 |
| 白色(1) | 黑线(0) | 左转 | LED1亮 |
| 黑线(0) | 白色(1) | 右转 | LED2亮 |
| 白色(1) | 白色(1) | 停止 | LED1+LED2灭 |

### 系统流程

1. 系统初始化
2. LED测试
3. OLED显示启动画面
4. 传感器校准
5. 进入主循环：
   - 读取传感器状态
   - 决策运动方向
   - 控制电机和LED
   - 更新OLED显示

## 功能特点

- **智能循迹**：基于双传感器的精确路径跟踪
- **状态指示**：LED灯直观显示转向状态
- **实时显示**：OLED屏幕显示详细状态信息
- **模块化设计**：代码结构清晰，易于维护和扩展
- **自动校准**：启动时自动校准传感器

## 调试说明

1. 确保硬件连接正确
2. 检查电源供电是否稳定
3. 观察OLED显示的传感器状态
4. 通过LED指示灯判断系统工作状态

## 扩展功能

可以在此基础上添加：
- 速度控制（PWM调速）
- 超声波避障
- 蓝牙遥控
- 路径记录和回放

## 注意事项

1. 确保使用11.0592MHz晶振
2. 循迹传感器需要适当调节灵敏度
3. 电机驱动电路需要足够的驱动能力
4. OLED显示屏地址默认为0x78（7位地址0x3C）
