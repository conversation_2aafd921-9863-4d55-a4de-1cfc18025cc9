#include "main.h"

// 全局变量定义
unsigned char car_status = CAR_STOP;
unsigned char track_left_status = TRACK_WHITE;
unsigned char track_right_status = TRACK_WHITE;
unsigned char system_mode = 0;  // 0: 正常模式, 1: 调试模式

/**
 * @brief 主函数
 */
void main(void)
{
    // 系统初始化
    System_Init();

    // LED测试
    LED_Test();

    // OLED初始化和启动画面
    OLED_Init();
    OLED_Show_Startup();
    Delay_ms(2000);

    // 显示系统信息
    OLED_Show_System_Info();
    Delay_ms(2000);

    // 传感器校准
    OLED_Clear();
    OLED_ShowString(0, 2, "Calibrating...");
    OLED_ShowString(0, 4, "Place on white");
    Delay_ms(2000);
    Track_Calibrate_White();

    OLED_ShowString(0, 4, "Place on black  ");
    Delay_ms(2000);
    Track_Calibrate_Black();

    // 准备开始
    OLED_Clear();
    OLED_ShowString(0, 2, "Ready to start!");
    OLED_ShowString(0, 4, "Track Following");
    Delay_ms(1000);

    // 主循环
    while(1)
    {
        // 读取循迹传感器状态
        Track_Read();

        // 使用决策函数确定小车动作
        car_status = Track_Decision();

        // 执行相应的动作
        switch(car_status)
        {
            case CAR_FORWARD:
                Motor_Forward();
                LED_Control(CAR_FORWARD);
                break;

            case CAR_LEFT:
                Motor_Left();
                LED_Control(CAR_LEFT);
                break;

            case CAR_RIGHT:
                Motor_Right();
                LED_Control(CAR_RIGHT);
                break;

            case CAR_STOP:
            default:
                Motor_Stop();
                LED_Control(CAR_STOP);
                break;
        }

        // 更新OLED显示
        OLED_Display_Status(car_status);

        // 控制循环频率
        Delay_ms(50);
    }
}

/**
 * @brief 系统初始化函数
 */
void System_Init(void)
{
    // 初始化所有电机控制引脚为低电平
    MOTOR_LEFT_1 = 0;
    MOTOR_LEFT_2 = 0;
    MOTOR_RIGHT_1 = 0;
    MOTOR_RIGHT_2 = 0;
    
    // 初始化LED为熄灭状态
    LED1 = 1;  // 共阳极LED，高电平熄灭
    LED2 = 1;
    
    // 初始化OLED引脚
    OLED_SCL = 1;
    OLED_SDA = 1;
}

/**
 * @brief 毫秒延时函数
 * @param ms 延时毫秒数
 */
void Delay_ms(unsigned int ms)
{
    unsigned int i, j;
    for(i = 0; i < ms; i++)
    {
        for(j = 0; j < 120; j++)  // 基于11.0592MHz晶振的延时
        {
            _nop_();
        }
    }
}
